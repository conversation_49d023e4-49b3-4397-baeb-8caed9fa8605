# School Year Migration System

## Overview
This document explains the automated school year migration system implemented in the Account model. The system handles the complete process of closing the current school year and initializing the next school year with proper balance migration.

**Important Note**: The `accounts` table (without suffix) is only used for SY 2024. Starting from SY 2025, all tables follow the naming convention with year suffix (e.g., `accounts_2025`, `accounts_2026`, etc.).

## Business Process: Overlapping School Year Enrollment

### Overview of Overlapping Periods
Schools operate with overlapping enrollment periods to generate revenue early and allow students flexibility in enrollment timing. This creates a period where two school years are active simultaneously.

### SY 2024 to SY 2025 Example Timeline

| Date Range | Period | SY 2024 Status | SY 2025 Status | Activities |
|------------|--------|----------------|----------------|------------|
| **Jan 1 - May 6, 2025** | Early Enrollment | Active (billing continues) | Reservations accepted | Students can reserve spots for SY 2025 |
| **May 7 - Apr 7, 2025** | Overlap Period | Active (billing continues) | Enrollment open | Both SY active, payments to respective accounts |
| **Apr 8 - May 23, 2025** | Grace Period | Billing ended | Active enrollment | SY 2024 grace period for final payments |
| **May 23, 2025** | Migration Day | **CLOSES** | Fully active | Outstanding balances migrated to old_balance |

### Key Dates from billing_cutoffs Table

**SY 2024:**
- Start Bill Date: `2024-08-07`
- **End Bill Date: `2025-04-07`** (Last billing for SY 2024)
- Opening Date: `2024-01-01`
- **Closing Date: `2025-05-23`** (Migration trigger date)

**SY 2025:**
- **Start Bill Date: `2025-05-07`** (Upon enrollment due date)
- End Bill Date: `2026-03-07`
- **Opening Date: `2025-01-01`** (Early enrollment begins)
- Closing Date: `2026-05-23`

### Detailed Business Process Flow

#### 1. Early Enrollment Period (Jan 1 - May 6, 2025)
- **Purpose**: Allow students to make reservations for SY 2025
- **System Behavior**:
  - Cashier module displays dropdown for both active (2024) and next (2025) school years
  - `accounts_2025` table is created when `opening_date` (Jan 1, 2025) arrives
  - Students can reserve spots but full enrollment not yet open
  - SY 2024 remains fully active with ongoing billing

#### 2. Overlap Period (May 7 - Apr 7, 2025)
- **Purpose**: Full enrollment for SY 2025 while SY 2024 billing continues
- **System Behavior**:
  - Both school years are active simultaneously
  - New enrollments tagged to SY 2025
  - Payments directed to respective accounts based on cashier selection
  - SY 2024 billing generation continues
  - Upon enrollment due date for SY 2025 is May 7, 2025

#### 3. Grace Period (Apr 8 - May 23, 2025)
- **Purpose**: Allow final payments for SY 2024 after billing ends
- **System Behavior**:
  - SY 2024 billing generation stops (after Apr 7, 2025)
  - Students can still make payments for SY 2024 outstanding balances
  - SY 2025 enrollment and payments continue normally
  - Most students expected to be fully paid by this time

#### 4. Migration Day (May 23, 2025)
- **Purpose**: Close SY 2024 and migrate outstanding balances
- **System Behavior**:
  - SY 2024 officially closes
  - Outstanding balances from SY 2024 transferred to `old_balance` in `accounts_2025`
  - Migration uses latest billing data (max billing ID per student) as source of truth
  - SY 2025 becomes the sole active school year

### Payment Processing During Overlap

#### Cashier Module Behavior
- **Dropdown Selection**: Cashier selects appropriate school year for each transaction
- **SY 2024 Payments**: Go to `accounts` table (or `accounts_2024`)
- **SY 2025 Payments**: Go to `accounts_2025` table
- **Dual Payments**: Students can pay for both school years in same session

#### Transaction Tracking
- **Transactions Table**: Contains `sy`/`esp` fields to identify school year
- **Ledgers Table**: Contains `sy`/`esp` fields for school year tracking
- **Accounts Table**: Serves as storage for total payments and balances per school year

### Revenue Recognition Strategy
- **Early Revenue**: SY 2025 payments immediately recognized in `accounts_2025`
- **Separation**: Each school year maintains separate financial records
- **Audit Trail**: `sy`/`esp` fields enable tracking and reconciliation
- **Check & Balance**: Accounts table enables retroactive verification

## Migration Timing Validation

### Overview
The system includes comprehensive date validation to ensure migrations only occur at appropriate times according to business rules. This prevents accidental data corruption and maintains system integrity during overlapping enrollment periods.

### Validation Periods

| Period | Migration Status | Description |
|--------|------------------|-------------|
| **Pre-Opening** | ❌ BLOCKED | Before early enrollment opens |
| **Early Enrollment** | ⚠️ WARNING | Early reservations active, requires override |
| **Overlap Period** | ⚠️ WARNING | Both SY active, not recommended |
| **Grace Period** | ⚠️ CAUTION | Acceptable but early, some payments may still come |
| **Post-Closing** | ✅ ALLOWED | Optimal timing for migration |

### Validation Rules

#### BLOCKED Periods
- **Pre-Opening**: Migration completely blocked until `opening_date`
- Cannot be overridden as next SY tables don't exist yet

#### WARNING Periods
- **Early Enrollment**: SY 2024 active, SY 2025 reservations only
- **Overlap Period**: Both school years fully active
- Requires `forceOverride=true` to proceed
- System warns about potential impacts

#### CAUTION Periods
- **Grace Period**: SY 2024 billing ended, but not yet closed
- Migration allowed but system recommends waiting
- Some students may still make final payments

#### ALLOWED Periods
- **Post-Closing**: Optimal timing after `closing_date`
- All SY 2024 activities completed
- Safe to migrate outstanding balances

## Available Options

### Migration Functions
The Account model provides the following functions for school year migration:

#### Main Functions
- `initializeNextSchoolYear($currentSY, $nextSY, $closingDate)` - Complete migration process
- `quickMigrate($fromSY, $toSY, $closingDate = null)` - Simplified migration with auto-defaults
- `getOutstandingBalancesSummary($sy, $closingDate)` - Get summary of balances

#### Internal Functions
- `createNextYearTables($nextSY)` - Create tables for next year
- `updateOutstandingBalancesFromBillings($currentSY, $closingDate)` - Update current balances
- `copyAccountsToNextYear($currentSY, $nextSY)` - Copy accounts to next year
- `migrateOutstandingBalances($currentSY, $nextSY)` - Migrate balances

#### Helper Functions
- `getAccountsTableName($sy)` - Get correct accounts table name for any SY
- `getAccountFeesTableName($sy)` - Get correct account_fees table name for any SY
- `getAccountSchedulesTableName($sy)` - Get correct account_schedules table name for any SY

#### Validation Functions
- `validateMigrationTiming($currentSY, $forceOverride)` - Check if migration is allowed based on current date
- `getBillingCutoffs($sy)` - Get billing cutoff dates from database

#### Configuration Management Functions
- `updateMasterConfig($configKey, $configValue)` - Update master_configs table
- `getMasterConfig($configKey)` - Get current master config value
- `updateSchoolYearConfigs($currentSY, $nextSY, $action)` - Handle SY transition configs

### Database Tables Created
For each school year, the following tables are created:
- `accounts_{YEAR}` - Main account information
- `account_fees_{YEAR}` - Fee breakdown per account
- `account_schedules_{YEAR}` - Payment schedules

### Table Naming Convention
- **SY 2024**: Uses `accounts` (no suffix), `account_fees`, `account_schedules`
- **SY 2025+**: Uses `accounts_2025`, `account_fees_2025`, `account_schedules_2025`, etc.

### Automatic Table Creation
The migration system **automatically creates tables** for the target school year. You don't need to run separate SQL files unless you want to create tables manually for testing purposes.

**SQL Files Provided (Optional):**
- `api/sql/250505_INIT_ACCOUNTS_2025.sql` - Creates SY 2025 tables
- `api/sql/250505_INIT_ACCOUNTS_2026.sql` - Creates SY 2026 tables

## How It Works

### Migration Process
The migration follows these steps:

1. **Create Next Year Tables**
   - Automatically creates target year tables (e.g., `accounts_2025`, `account_fees_2025`, `account_schedules_2025`)
   - Tables are created with proper structure and indexes
   - Uses helper functions to determine correct table names

2. **Update Current Year Balances**
   - Uses the latest billing records to determine final outstanding balances
   - Updates the current year's accounts table with the most recent billing data
   - Uses the logic: largest billing ID = most recent billing
   - Automatically detects source table (`accounts` for SY 2024, `accounts_YYYY` for SY 2025+)

3. **Copy Accounts to Next Year**
   - Copies all account records from source table to target table
   - Resets financial fields to zero
   - Sets `sy` field to the new school year
   - Sets `account_details` to 'INIT_ACCOUNT'
   - Handles table naming automatically

4. **Migrate Outstanding Balances**
   - Transfers `outstanding_balance` from current year to `old_balance` in next year
   - Only accounts with non-zero balances are updated
   - Works across different table naming conventions

### SQL Queries Used

#### Update Outstanding Balances from Latest Billings
```sql
UPDATE
  accounts a1
  INNER JOIN
    (SELECT
      b1.`account_id`,
      b1.`due_amount`
    FROM
      billings b1
      INNER JOIN
        (SELECT
          account_id,
          MAX(id) AS latest_billing_id
        FROM
          billings
        WHERE due_date = '2025-04-07'
          AND sy = 2024
        GROUP BY account_id) AS b2
        ON (b1.`id` = b2.latest_billing_id)) b3
    ON (b3.account_id = a1.id)
SET a1.`outstanding_balance` = b3.due_amount;
```

#### Migrate Balances to Next Year

**For SY 2024 to SY 2025:**
```sql
UPDATE accounts_2025 a25
INNER JOIN accounts a24 ON (a24.id = a25.id)
SET a25.`old_balance` = a24.`outstanding_balance`
WHERE a24.`outstanding_balance` != 0;
```

**For SY 2025 to SY 2026:**
```sql
UPDATE accounts_2026 a26
INNER JOIN accounts_2025 a25 ON (a25.id = a26.id)
SET a26.`old_balance` = a25.`outstanding_balance`
WHERE a25.`outstanding_balance` != 0;
```

## Usage Examples

### Quick Migration with Validation (Recommended)
```php
// Initialize Account model
App::import('Model', 'Account');
$Account = new Account();

// Simple migration with automatic validation
$result = $Account->quickMigrate(2024, 2025); // Auto-validates timing
$result = $Account->quickMigrate(2025, 2026); // Auto-validates timing

// Force migration even if validation fails (for testing)
$result = $Account->quickMigrate(2024, 2025, null, true); // forceOverride=true

// Custom closing date with validation
$result = $Account->quickMigrate(2025, 2026, '2026-03-31', false);

if ($result['success']) {
    echo "Migration successful!";
    echo "Accounts updated: " . $result['accounts_updated'];
    echo "Balances migrated: " . $result['balances_migrated'];

    // Check for warnings
    if (!empty($result['warnings'])) {
        echo "Warnings: " . implode(', ', $result['warnings']);
    }
} else {
    echo "Migration failed: " . $result['message'];

    // Show validation details
    if (!empty($result['validation'])) {
        $val = $result['validation'];
        echo "Current period: " . $val['current_period'];
        echo "Status: " . $val['status'];
    }
}
```

### Full Migration Control
```php
// For complete control over the migration process
$result = $Account->initializeNextSchoolYear(2024, 2025, '2025-04-07', false);

// Force migration regardless of validation
$result = $Account->initializeNextSchoolYear(2024, 2025, '2025-04-07', true);

if ($result['success']) {
    echo "Migration successful!";
    echo "Accounts updated: " . $result['accounts_updated'];
    echo "Balances migrated: " . $result['balances_migrated'];
} else {
    echo "Migration failed: " . $result['message'];

    // Check validation results
    if (!empty($result['validation'])) {
        $val = $result['validation'];
        echo "Validation Status: " . $val['status'];
        echo "Current Period: " . $val['current_period'];
        echo "Migration Allowed: " . ($val['allowed'] ? 'YES' : 'NO');
    }
}
```

### Validation Only
```php
// Check migration timing without performing migration
$validation = $Account->validateMigrationTiming(2024, false);

echo "Current Date: " . $validation['current_date'];
echo "Current Period: " . $validation['current_period'];
echo "Migration Status: " . $validation['status'];
echo "Migration Allowed: " . ($validation['allowed'] ? 'YES' : 'NO');

if (!empty($validation['warnings'])) {
    echo "Warnings:";
    foreach ($validation['warnings'] as $warning) {
        echo "- " . $warning;
    }
}

if (!empty($validation['recommendations'])) {
    echo "Recommendations:";
    foreach ($validation['recommendations'] as $rec) {
        echo "- " . $rec;
    }
}
```

### Get Summary Before Migration
```php
// Get summary of outstanding balances
$summary = $Account->getOutstandingBalancesSummary(2024, '2025-04-07');

echo "Total Accounts: " . $summary['total_accounts'];
echo "Accounts with Balance: " . $summary['accounts_with_balance'];
echo "Total Outstanding: ₱" . number_format($summary['total_outstanding'], 2);
```

### Using Helper Functions
```php
// Get correct table names for any school year
echo "SY 2024 accounts table: " . $Account->getAccountsTableName(2024); // "accounts"
echo "SY 2025 accounts table: " . $Account->getAccountsTableName(2025); // "accounts_2025"
echo "SY 2026 accounts table: " . $Account->getAccountsTableName(2026); // "accounts_2026"

echo "SY 2024 fees table: " . $Account->getAccountFeesTableName(2024); // "account_fees"
echo "SY 2025 fees table: " . $Account->getAccountFeesTableName(2025); // "account_fees_2025"

echo "SY 2024 schedules table: " . $Account->getAccountSchedulesTableName(2024); // "account_schedules"
echo "SY 2025 schedules table: " . $Account->getAccountSchedulesTableName(2025); // "account_schedules_2025"
```

### Using the Migration Script

**For SY 2024 to SY 2025:**
```bash
# Run from command line
php api/sql/migrate_sy_2024_to_2025.php

# Or access via web browser
http://yoursite.com/api/sql/migrate_sy_2024_to_2025.php
```

**For SY 2025 to SY 2026:**
```bash
# Run from command line
php api/sql/migrate_sy_2025_to_2026.php

# Or access via web browser
http://yoursite.com/api/sql/migrate_sy_2025_to_2026.php
```

**Quick Migration Examples:**
```bash
# Run quick migration examples
php api/sql/quick_migrate_example.php

# Or access via web browser
http://yoursite.com/api/sql/quick_migrate_example.php
```

**Validation Scripts:**
```bash
# Check migration timing and validation
php api/sql/validate_migration_timing.php

# Smart migration with comprehensive validation
php api/sql/smart_migrate_sy_2024_to_2025.php

# Or access via web browser
http://yoursite.com/api/sql/validate_migration_timing.php
http://yoursite.com/api/sql/smart_migrate_sy_2024_to_2025.php
```

## What If / Edge Cases

### What if the migration fails?
- Check the error messages in the result array
- Verify that the billings table has data for the closing date
- Ensure database permissions allow table creation
- Check that the current year accounts table exists (use helper functions to verify table names)
- Verify the source table name is correct for the school year

### What if some accounts don't have billing records?
- Only accounts with billing records on the closing date will be updated
- Accounts without billing records will retain their current outstanding_balance
- This is expected behavior for inactive accounts

### What if I need to re-run the migration?
- The migration can be safely re-run
- Tables are truncated before copying, so duplicate records won't occur
- Outstanding balances will be recalculated from the latest billing data
- The system automatically detects correct table names for re-runs

### What if the closing date changes?
- Simply run the migration again with the new closing date
- The system will recalculate balances based on the new date
- Previous migration results will be overwritten
- Use `quickMigrate()` with custom date: `$Account->quickMigrate(2024, 2025, '2025-03-31')`

### What about table creation?
- **Automatic**: Tables are created automatically during migration
- **Manual**: You can run the SQL files manually if needed for testing
- **Verification**: Use helper functions to check if tables exist before migration

## Sample Migration Results

```
=== School Year Migration: SY 2024 to SY 2025 ===

Configuration:
- Current SY: 2024
- Next SY: 2025
- Closing Date: 2025-04-07

Summary for SY 2024 (before migration):
- Total Accounts: 1,250
- Accounts with Balance: 45
- Fully Paid Accounts: 1,205
- Total Outstanding: ₱125,750.00
- Average Balance: ₱2,794.44

Migration Results:
- Success: YES
- Message: Successfully initialized SY 2025 and migrated balances from SY 2024
- Accounts Updated: 1,250
- Balances Migrated: 45

Steps Completed:
✓ Created tables for SY 2025
✓ Updated 1,250 accounts with latest billing balances
✓ Copied accounts to accounts_2025 table
✓ Migrated 45 outstanding balances to SY 2025

Verification:
- Table accounts_2025: 1,250 records
- Table account_fees_2025: 0 records
- Table account_schedules_2025: 0 records
- Accounts with old_balance: 45
- Total old_balance amount: ₱125,750.00
- Original outstanding balance: ₱125,750.00
✓ Balance migration verified - amounts match!

=== Migration Complete ===
```

## Future Migrations

For future school years (2025 to 2026, 2026 to 2027, etc.), the system automatically handles table naming:

### Quick Migration (Recommended)
```php
// Simple one-line migrations with automatic table detection
$Account->quickMigrate(2025, 2026); // accounts_2025 → accounts_2026
$Account->quickMigrate(2026, 2027); // accounts_2026 → accounts_2027
$Account->quickMigrate(2027, 2028); // accounts_2027 → accounts_2028
```

### Full Control Migration
```php
// For complete control over the process
$result = $Account->initializeNextSchoolYear(2025, 2026, '2026-04-07');
$result = $Account->initializeNextSchoolYear(2026, 2027, '2027-04-07');
```

### Benefits of the Updated System
- **Automatic Table Detection**: No need to worry about table naming conventions
- **Consistent Process**: Same function works for all school year transitions
- **Error Prevention**: Helper functions prevent table name mistakes
- **Future-Proof**: Works for any future school year (2025, 2026, 2027, etc.)
- **Comprehensive Reporting**: Detailed feedback on migration progress

This migration system ensures consistent and reliable transfer of outstanding balances between school years while automatically handling the different table naming conventions for SY 2024 vs SY 2025+ and providing comprehensive reporting.
